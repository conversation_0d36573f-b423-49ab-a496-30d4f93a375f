# GMX Email Creator

A professional, fast, and reliable tool for creating GMX.de email accounts automatically.

## Features

- Automated GMX.de email account creation through the 6-step registration process
- Advanced stealth features to avoid bot detection:
  - Browser fingerprint protection (canvas, WebGL, audio)
  - Human-like typing with realistic delays, typos, and pauses
  - Human-like mouse movements and interactions
  - Randomized behavior patterns
  - Anti-detection browser launch parameters
- Handles GMX tracking consent page automatically
- HMA VPN integration for German IP address requirements
- Automatic IP rotation to avoid detection
- Fast browser automation using Playwright with human-like interaction
- SMS verification using sms-activate.io with phone number rotation
- 2captcha integration for solving captchas (if needed)
- German user data generation using randomuser.me API
- Unlimited account creation mode
- Detailed logging and error handling
- Configurable settings via .env file or command-line arguments
- Saves created accounts to a text file

## Requirements

- Python 3.8+
- Playwright
- 2captcha API key
- SMS-Activate.io API key
- HMA VPN (optional, for German IP address)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/gmx-creator.git
cd gmx-creator
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install Playwright browsers:
```bash
playwright install
```

5. Configure your settings:
   - Copy the `.env.example` file to `.env` (or let the program create it for you on first run)
   - Add your 2captcha API key
   - Add your SMS-Activate.io API key
   - Adjust other settings as needed

## Usage

### Basic Usage

```bash
python gmx_creator.py
```

This will create one GMX email account.

### Advanced Usage

```bash
# Create 5 accounts
python gmx_creator.py --count 5

# Run in headless mode
python gmx_creator.py --headless

# Use Firefox browser
python gmx_creator.py --browser firefox

# Specify API keys directly
python gmx_creator.py --sms-api-key YOUR_SMS_API_KEY --2captcha-api-key YOUR_2CAPTCHA_API_KEY

# Configure phone number rotation
python gmx_creator.py --max-phones 5 --no-phone-rotation
```

### Important Notes

1. **German IP Required**: You will need a German IP address to access the GMX registration page. The script integrates with HMA VPN to automatically provide a German IP address and rotate IPs as needed. If you don't have HMA VPN installed, you can use the `--no-vpn` flag and provide your own German proxy or VPN.

2. **Gender Selection**: The script will randomly select a gender (female, male, or neutral) during registration.

3. **Password Generation**: The script generates strong passwords that meet GMX's requirements (at least 8 characters with a mix of uppercase, lowercase, digits, and special characters).

4. **Phone Number Rotation**: The script will rent a limited number of phone numbers (default: 3) and reuse them for multiple account creations during the 4-hour rental period. This maximizes the number of accounts you can create with a limited budget.

### Command Line Arguments

- `--count`, `-c`: Number of accounts to create (default: 1, 0 for unlimited)
- `--unlimited`: Create unlimited accounts (same as --count 0)
- `--headless`: Run in headless mode (no visible browser)
- `--browser`: Browser to use (chromium, firefox, webkit)
- `--sms-api-key`: SMS-Activate API key (overrides .env setting)
- `--2captcha-api-key`: 2captcha API key (overrides .env setting)
- `--max-phones`: Maximum number of phone numbers to keep in rotation (default: 3)
- `--no-phone-rotation`: Disable phone number rotation
- `--no-vpn`: Disable HMA VPN integration
- `--no-stealth`: Disable stealth mode

## Configuration

You can configure the application by editing the `.env` file:

```
# API Keys
TWOCAPTCHA_API_KEY=your_2captcha_api_key_here
SMS_ACTIVATE_API_KEY=your_sms_activate_api_key_here

# Proxy Settings (optional)
USE_PROXY=False
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# SMS Verification Settings
SMS_COUNTRY_CODE=49
SMS_SERVICE_CODE=gmx
SMS_PHONE_ROTATION=True
SMS_MAX_PHONES=3
```

Additional settings can be modified in `config.py`.

## Output

Created accounts are saved to `created_accounts.txt` in the format:
```
<EMAIL>;password
```

## Logging

Detailed logs are saved to `gmx_creator.log` and also displayed in the console.

## Troubleshooting

### Captcha Issues

If you're experiencing issues with captcha solving:
- Ensure your 2captcha API key is correct
- Check your 2captcha balance
- Try running with visible browser (without --headless)

### SMS Verification Issues

If you're experiencing issues with SMS verification:
- Ensure your SMS-Activate.io API key is correct
- Check your SMS-Activate.io balance
- Try using a different country code
- Make sure the service code for GMX is correct (may change over time)
- Try disabling phone rotation with `--no-phone-rotation`

### Phone Number Rotation Strategy

The script uses an efficient phone number rotation strategy to maximize the number of accounts you can create with a limited budget:

1. It rents a limited number of phone numbers (default: 3) for a 4-hour period
2. It reuses these phone numbers for multiple account creations during that period
3. With a $2 balance and 3 phone numbers (costing about $0.31 each), you can create many accounts during the 4-hour rental period

You can configure this behavior with:
- `--max-phones`: Set the maximum number of phone numbers to keep in rotation (2-3 recommended for a $2 balance)
- `--no-phone-rotation`: Disable phone number rotation and use a new number for each account

### Browser Automation Issues

If the browser automation is failing:
- Update Playwright: `pip install --upgrade playwright`
- Reinstall browsers: `playwright install`
- Try a different browser with `--browser firefox`

## Legal Disclaimer

This tool is provided for educational and research purposes only. Users are responsible for ensuring their use of this tool complies with GMX's terms of service and applicable laws. The authors are not responsible for any misuse of this software.

## License

MIT License
