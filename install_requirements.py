#!/usr/bin/env python3
"""
Installation script for Groups Management System dependencies
Comprehensive package installation for groups.py and updated_groups.py
"""

import subprocess
import sys
import os


def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False



def check_package(package):
    """Check if a package is already installed"""
    try:
        # Handle special cases for package name mapping
        import_name = package
        if package == "pillow":
            import_name = "PIL"
        elif package == "opencv-python":
            import_name = "cv2"
        elif package == "pyautogui":
            import_name = "pyautogui"
        elif package == "unidecode":
            import_name = "unidecode"
        elif package == "psutil":
            import_name = "psutil"
        elif package == "numpy":
            import_name = "numpy"
        elif package == "2captcha-python":
            import_name = "twocaptcha"
        elif package == "anticaptchaofficial":
            import_name = "anticaptchaofficial"
        elif package == "seleniumbase":
            import_name = "seleniumbase"
        elif package == "fake-useragent":
            import_name = "fake_useragent"
        elif package == "webdriver-manager":
            import_name = "webdriver_manager"
        elif package == "undetected-chromedriver":
            import_name = "undetected_chromedriver"
        elif package == "selenium-wire":
            import_name = "seleniumwire"
        elif package == "python-dotenv":
            import_name = "dotenv"
        elif package == "selenium-stealth":
            import_name = "selenium_stealth"
        elif package == "pytz":
            import_name = "pytz"

        __import__(import_name)
        return True
    except ImportError:
        return False


def check_system_requirements():
    """Check system-specific requirements and provide recommendations"""
    print("\n🔍 Checking system requirements...")

    # Check Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

    if python_version < (3, 7):
        print("❌ Python 3.7+ is required for this application")
        return False
    elif python_version < (3, 8):
        print("⚠️ Python 3.8+ is recommended for best compatibility")
    else:
        print("✅ Python version is compatible")

    # Check operating system
    import platform
    os_name = platform.system()
    print(f"💻 Operating System: {os_name}")

    if os_name == "Windows":
        print("✅ Windows detected - all features should work")
        print("📝 Note: Make sure Chrome browser is installed")
    elif os_name == "Linux":
        print("✅ Linux detected")
        print("📝 Note: You may need to install additional packages:")
        print("   sudo apt-get install python3-tk xvfb  # For GUI and headless support")
    elif os_name == "Darwin":  # macOS
        print("✅ macOS detected")
        print("📝 Note: Make sure Chrome browser is installed")
    else:
        print(f"⚠️ Unsupported OS: {os_name}")

    # Check if Chrome is available
    chrome_paths = [
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/google-chrome-stable",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]

    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ Chrome found at: {path}")
            chrome_found = True
            break

    if not chrome_found:
        print("⚠️ Chrome browser not found in standard locations")
        print("📝 Please install Google Chrome from: https://www.google.com/chrome/")

    return True


def main():
    """Main installation function"""
    print("🚀 Groups Management System - Dependency Installation")
    print("=" * 60)
    print("📋 Analyzing imports from groups.py and updated_groups.py...")

    # Check system requirements first
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please upgrade Python and try again.")
        return False

    # Core Python packages (usually included with Python)
    core_packages = [
        "requests",
        "json",  # Built-in
        "os",    # Built-in
        "time",  # Built-in
        "random", # Built-in
        "logging", # Built-in
        "subprocess", # Built-in
        "threading", # Built-in
        "sqlite3", # Built-in
        "pickle", # Built-in
        "base64", # Built-in
        "math",   # Built-in
        "secrets", # Built-in
        "tempfile", # Built-in
        "socket", # Built-in
        "re",     # Built-in
        "hashlib", # Built-in
        "urllib.parse", # Built-in
        "pathlib", # Built-in
        "datetime", # Built-in
        "typing", # Built-in
        "dataclasses", # Built-in
        "http.server", # Built-in
        "socketserver", # Built-in
        "shutil", # Built-in
        "sys",    # Built-in
        "msvcrt", # Built-in (Windows)
        "string", # Built-in
        "asyncio" # Built-in (Python 3.7+)
    ]

    # Required packages for core functionality
    required_packages = [
        "selenium",              # Web automation framework
        "webdriver-manager",     # Automatic webdriver management
        "requests",              # HTTP library
        "numpy",                 # Numerical computing
        "pillow",                # Image processing (PIL)
        "pyautogui",             # GUI automation
        "unidecode",             # Unicode text unidecode
        "psutil",                # System and process utilities
    ]

    # SeleniumBase and enhanced automation packages
    seleniumbase_packages = [
        "seleniumbase",          # Enhanced Selenium framework
        "undetected-chromedriver", # Undetected Chrome automation
    ]

    # CAPTCHA solving packages
    captcha_packages = [
        "2captcha-python",       # 2captcha API client
        "anticaptchaofficial",   # Anti-captcha API client
    ]

    # Web scraping and parsing packages
    scraping_packages = [       # HTML/XML parsing
        "selenium-wire",         # Selenium with request/response capture
    ]

    # Computer vision packages
    vision_packages = [
        "opencv-python",         # Computer vision library
    ]

    # Utility packages
    utility_packages = [
        "fake-useragent",        # User agent rotation
        "python-dotenv",         # Environment variable management
    ]

    # Stealth packages
    stealth_packages = [
        "selenium-stealth",      # Selenium stealth fingerprint masking
        "pytz",                  # Timezone handling for proxy matching
        "requests",              # For timezone/geolocation API calls
    ]

    installed_count = 0
    failed_count = 0
    skipped_count = 0

    # Install packages in order of importance
    package_groups = [
        ("Core Required Packages", required_packages, True),
        ("SeleniumBase Packages", seleniumbase_packages, True),
        ("Stealth Packages", stealth_packages, True),
        ("CAPTCHA Solving Packages", captcha_packages, False),
        ("Web Scraping Packages", scraping_packages, False),
        ("Computer Vision Packages", vision_packages, False),
        ("Utility Packages", utility_packages, False)
    ]

    for group_name, packages, is_required in package_groups:
        print(f"\n📦 Installing {group_name}...")

        for package in packages:
            if check_package(package):
                print(f"✅ {package} is already installed")
                continue

            print(f"📥 Installing {package}...")
            if install_package(package):
                installed_count += 1
            else:
                if is_required:
                    failed_count += 1
                    print(f"❌ CRITICAL: Required package {package} failed to install!")
                else:
                    skipped_count += 1
                    print(f"⚠️ Optional package {package} failed to install (not critical)")

    # Special handling for tkinter (usually comes with Python but might be missing on some systems)
    print(f"\n🔍 Checking for tkinter (GUI library)...")
    try:
        import tkinter
        print("✅ tkinter is available")
    except ImportError:
        print("⚠️ tkinter not available - some GUI features may not work")
        print("   On Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   On CentOS/RHEL: sudo yum install tkinter")
        print("   On macOS: tkinter should be included with Python")

    print(f"\n📊 Installation Summary:")
    print(f"   Packages installed: {installed_count}")
    print(f"   Required packages failed: {failed_count}")
    print(f"   Optional packages skipped: {skipped_count}")
    print(f"   Total packages processed: {installed_count + failed_count + skipped_count}")

    if failed_count == 0:
        print("\n🎉 All critical dependencies installed successfully!")
        print("\n🚀 You can now run the Groups Management System:")
        print("   python groups.py")
        print("\n📚 Available features:")
        print("   ✅ Enhanced SeleniumBase automation")
        print("   ✅ Profile management")
        print("   ✅ Behavioral simulation")
        if skipped_count == 0:
            print("   ✅ CAPTCHA solving (2captcha, anticaptcha)")
            print("   ✅ Computer vision (OpenCV)")
            print("   ✅ Advanced web scraping")
        else:
            print(f"   ⚠️ Some optional features unavailable ({skipped_count} packages not installed)")
    else:
        print(f"\n❌ {failed_count} critical packages failed to install.")
        print("Please install them manually or check your internet connection.")
        print("\nManual installation commands:")
        for group_name, packages, is_required in package_groups:
            if is_required:
                for package in packages:
                    if not check_package(package):
                        print(f"   pip install {package}")

    return failed_count == 0


def create_requirements_file():
    """Create a requirements.txt file with all dependencies"""
    requirements_content = """# Groups Management System Dependencies
# Generated by install_requirements.py

# Core automation packages
selenium>=4.0.0
seleniumbase>=4.0.0
webdriver-manager>=3.8.0
undetected-chromedriver>=3.4.0

# Stealth and fingerprint masking
selenium-stealth>=1.0.6
pytz>=2021.3

# HTTP and networking
requests>=2.25.0

# Data processing and utilities
numpy>=1.20.0
pillow>=8.0.0
pyautogui>=0.9.50
unidecode>=1.2.0
psutil>=5.8.0

# CAPTCHA solving (optional)
2captcha-python>=1.1.0
anticaptchaofficial>=1.0.0

# Web scraping (optional)
selenium-wire>=5.0.0

# Computer vision (optional)
opencv-python>=4.5.0

# Utilities (optional)
fake-useragent>=1.1.0
python-dotenv>=0.19.0

# Note: Some packages may require additional system dependencies
# See install_requirements.py for system-specific installation instructions
"""

    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        print("📄 Created requirements.txt file")
        return True
    except Exception as e:
        print(f"⚠️ Could not create requirements.txt: {e}")
        return False


if __name__ == "__main__":
    print("🎯 Groups Management System - Dependency Installer")
    print("📦 This script will install all required packages for groups.py and updated_groups.py")
    print()

    # Create requirements.txt file
    create_requirements_file()

    # Run main installation
    success = main()

    if success:
        print("\n" + "=" * 60)
        print("🎉 Installation completed successfully!")
        print("📚 Next steps:")
        print("   1. Run: python groups.py")
        print("   2. Check the application logs for any runtime issues")
        print("   3. Refer to the documentation for configuration options")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ Installation failed!")
        print("🔧 Troubleshooting:")
        print("   1. Check your internet connection")
        print("   2. Try running: pip install --upgrade pip")
        print("   3. Install packages manually using: pip install <package_name>")
        print("   4. Check the error messages above for specific issues")
        print("=" * 60)
        sys.exit(1)
